<template>
  <view class="box-sizing-b w-full">
    <!-- 01. 头部组件 -->
    <use-header search-tip="请输入搜索关键字" :search-auto="true"></use-header>

    <!-- 02. 轮播区 -->
    <view class="swiper-area pos-r" v-if="swiperDatas && swiperDatas.length > 0">
      <!-- 轮播组件 -->
      <swiper class="swiper w-full" autoplay indicator-dots indicator-color="#f7f7f7" indicator-active-color="#428675">
        <swiper-item class="swiper-item padding-lr wh-full box-sizing-b" v-for="(item, index) in swiperDatas"
                     :key="index">
          <view class="wh-full" @click.stop="topage(item)">
            <image class="border-radius wh-full" mode="aspectFill" :lazy-load="true" :src="item.image"/>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 03. 分类区 -->
    <view class="category-area dflex dflex-wrap-w" v-if="categoryDatas && categoryDatas.length > 0">
      <view class="category-item dflex dflex-flow-c margin-bottom-sm" v-for="(item, index) in categoryDatas"
            :key="index" @click="toCategory(item.id)">
        <image class="margin-bottom-xs" lazy-load :src="item.image"></image>
        <text class="tac clamp">{{ item.name }}</text>
      </view>
    </view>
    <view class="gap"></view>

    <!-- 04. 限时精选 -->
    <use-list-title title="限时精选" size="32" fwt="600" color="#333" iconfont="icondaishouhuo-" >
    </use-list-title>
    <view class="limit-area bg-white">
      <scroll-view class="padding-lr" scroll-x>
        <view class="dflex padding-bottom">
          <view class="item margin-right-sm" v-for="(item, index) in goodsLimitDatas" :key="index"
                @click="toGoods(item.id)">
            <image class="border-radius-xs" mode="aspectFill" :lazy-load="true" :src="item.image"></image>
            <text class="title clamp padding-bottom-xs">{{ item.name }}</text>
            <text class="price">{{ item.actualPrice }}</text>
            <text class="m-price">{{ item.originalPrice }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="gap"></view>

    <!-- 05. 热门推荐 -->
    <use-hot-goods :datas="goodsHotDatas" autoload="none" title="热门推荐"></use-hot-goods>

    <!-- 置顶 -->
    <use-totop ref="useTop" :style="{ marginBottom: '50px' }"></use-totop>


  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onLoad, onPageScroll, onShow, onPullDownRefresh} from '@dcloudio/uni-app'
import {getBannerListData, getCategoryListData} from "@/common/api"
import {getRecommendedListData} from "@/common/api/goods"
import {useWxJs} from "@/common/utils";

const {share} = useWxJs()
const swiperDatas = ref([])
const categoryDatas = ref([])
const goodsLimitDatas = ref([])
const goodsHotDatas = ref([])

const useTop = ref(null)

const loadData = async () => {
  swiperDatas.value = await getBannerListData()
  categoryDatas.value = await getCategoryListData()
  goodsLimitDatas.value = await getRecommendedListData("limit")
  goodsHotDatas.value = await getRecommendedListData("hot")
}

const toCategory = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods-list?category_id=${id}`
  })
}
const toGoods = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods?id=${id}`
  })
}
const topage = (item) => {
  // 处理轮播图点击事件，可以根据item的属性进行不同的跳转
  if (item.url) {
    uni.navigateTo({
      url: item.url
    })
  }
}

onPageScroll(e => {
  useTop.value.change(e.scrollTop);
})

onShow(() => {
  loadData()
  share()
})

onPullDownRefresh(async () => {
  await loadData()
  uni.stopPullDownRefresh()
})

onLoad(() => {
})
</script>

<style lang="scss">

/* 轮播图区 */
.swiper-area {
  .swiper {
    height: 240rpx;
  }
}

/* 分类区 */
.category-area {
  padding: 60rpx 0 30rpx 0;

  .category-item {
    font-size: $font-sm + 2upx;
    color: $font-color-dark;
    width: 25%;
  }

  image {
    width: 96rpx;
    height: 96rpx;
  }
}

/* 限时精选区 */
.limit-area {
  min-height: 240rpx;

  .item {
    width: 240rpx;

    image {
      width: 240rpx;
      height: 240rpx;
    }
  }
}

</style>
