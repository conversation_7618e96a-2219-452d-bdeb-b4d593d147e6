<template>
  <view class="reseller">

    <!--合伙人基本信息-->
    <view v-if="partner && partner.name" class="partner-header bg-white mx-4 mt-4 rounded-2xl shadow-sm border border-gray-100 mb-4">
      <view class="p-6">
        <!-- 合伙人欢迎信息 -->
        <view class="flex items-center justify-between mb-4">
          <view class="flex items-center">
            <view class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
              <text class="text-white text-lg font-bold">{{ partner.name.charAt(0) }}</text>
            </view>
            <view>
              <text class="text-lg font-semibold text-gray-800">尊敬的合伙人{{ partner.name }}</text>
              <view class="flex items-center mt-1">
                <view class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full mr-2">
                  {{ getPartnerLevelName(partner.partnerLevelId) }}
                </view>
                <text class="text-xs text-gray-500">{{ formatDate(partner.proxyStartDate) }}加入</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 合伙人详细信息 -->
        <view class="grid grid-cols-2 gap-4 text-sm">
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">代理区域</text>
            <text class="font-medium text-gray-800">{{ partner.proxyArea || '全国' }}</text>
          </view>
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">提成权限</text>
            <text class="font-medium text-gray-800">{{ partner.commission || '待设置' }}</text>
          </view>
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">押金余额</text>
            <text class="font-medium text-gray-800">¥{{ partner.depositAmount || 0 }}</text>
          </view>
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">押金状态</text>
            <text class="font-medium" :class="getDepositStatusClass(partner.depositStatus)">
              {{ getDepositStatusText(partner.depositStatus) }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!--合伙人收益信息-->
    <view class="top mx-4 mt-4 rounded-2xl shadow-sm border border-gray-100 mb-4">
      <view class="num" style="margin-left: 30px;">
        <view>累计收益</view>
        <view class="txc">{{incomeTotal}}</view>
      </view>
      <view class="num1" style="">
        <view>当前提成</view>
        <view style="font-size: 40px;">{{commission}}</view>
      </view>
      <view class="num" style="margin-right: 30px;">
        <view>累计已提</view>
        <view>{{withdrawTotal}}</view>
      </view>
      <view class="tixian" @click="jump_cash">
        <view class="btn bg-mint" style="font-size: 16px;">立即提现</view>
      </view>
      <!--<view class="jilu">-->
      <!--  <navigator url="/pages/cash/record">-->
      <!--    <view class="jl">提现记录 ></view>-->
      <!--  </navigator>-->
      <!--</view>-->
    </view>

    <view class="icon">
      <view class="ico">
        <navigator url="/pages/partner/subordinate">
          <view class="tubiao">
            <image class="img" src="/static/images/subordinate.png"></image>
          </view>
          <view class="tt">
            <view class="text">下级用户</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/partner/commission">
          <view class="tubiao">
            <image class="img" src="/static/images/pie.png"></image>
          </view>
          <view class="tt">
            <view class="text">订单提成</view>
          </view>
        </navigator>
      </view>

      <view class="ico">
        <navigator url="/pages/partner/monthly">
          <view class="tubiao">
            <image class="img" src="/static/images/monthly.png"></image>
          </view>
          <view class="tt">
            <view class="text">月度奖励</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/partner/annual">
          <view class="tubiao">
            <image class="img" src="/static/images/annual.png"></image>
          </view>
          <view class="tt">
            <view class="text">年度奖励</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/partner/deposit">
          <view class="tubiao">
            <image class="img" src="/static/images/deposit.png"></image>
          </view>
          <view class="tt">
            <view class="text">押金记录</view>
          </view>
        </navigator>
      </view>
      <view class="ico">
        <navigator url="/pages/cash/record">
          <view class="tubiao">
            <image class="img" src="/static/images/money.png"></image>
          </view>
          <view class="tt">
            <view class="text">提现记录</view>
          </view>
        </navigator>
      </view>
    </view>
    <view class="kong"></view>
  </view>
</template>


<script setup lang="ts">
import {ref} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getWithdrawMoneyAvailableData, getWithdrawIncomeTotalData, getWithdrawTotalData} from "@/common/api/withdraw"
import {getPartnerInfoData} from "@/common/api/partner"
import {useWxJs} from "@/common/utils";

interface Partner {
  name: string; //合伙人姓名，尊敬的合伙人xxx
  partnerLevelId: string; // 合伙人级别ID
  level: string; // 合伙人等级
  proxyStartDate: string; // 合伙人开始时间 yyyy-MM-dd HH:mm:ss
  depositAmount: number; // 押金余额
  proxyArea: string; // 代理区域
  commission: string; // 等级提成权限
  depositStatus: string; //押金状态:0=未缴纳,1=部分缴纳,2=完全缴纳,3=已退还
}

const partner = ref<Partner>({})

const getPartnerInfo = async () => {
  partner.value = await getPartnerInfoData()
  if (!partner.value || !partner.value.name) {
    uni.showModal({
      title: '提示',
      content: '您还不是合伙人，请先申请',
      showCancel: false,
      success: () => {
        uni.redirectTo({
          url: '/pages/partner/apply'
        })
      }
    })
  }
  partner.value.commission = partnerLevels.value.find(l => l.id === partner.value.partnerLevelId)?.commission || '待设置'
}

// 合伙人级别配置
const partnerLevels = ref([
  {
    id: '1',
    name: '资源引荐官',
    deposit: 0,
    commission: '最高15%（一次性）',
    features: ['无门槛', '一次性分成', '仅对接资源']
  },
  {
    id: '2',
    name: 'T1级超级合伙人',
    deposit: 5000,
    commission: '9%（持续）',
    features: ['5000元押金', '持续性分成', '兼职拓展']
  },
  {
    id: '3',
    name: 'T2级超级合伙人',
    deposit: 10000,
    commission: '12%（持续）',
    features: ['10000元押金', '持续性分成', '全职运营']
  },
  {
    id: '4',
    name: 'T3级超级合伙人',
    deposit: 50000,
    commission: '15%（持续）+ 年度奖励',
    features: ['50000元押金', '持续性分成', '团队化运作']
  }
]);

// 获取合伙人等级名称
const getPartnerLevelName = (levelId: string) => {
  const level = partnerLevels.value.find(l => l.id === levelId)
  return level ? level.name : '未知等级'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取押金状态文本
const getDepositStatusText = (status: string) => {
  const statusMap = {
    '0': '未缴纳',
    '1': '部分缴纳',
    '2': '完全缴纳',
    '3': '已退还'
  }
  return statusMap[status] || '未知状态'
}

// 获取押金状态样式类
const getDepositStatusClass = (status: string) => {
  const classMap = {
    '0': 'text-red-600',
    '1': 'text-yellow-600',
    '2': 'text-green-600',
    '3': 'text-gray-600'
  }
  return classMap[status] || 'text-gray-600'
}

const commission = ref(0)
const incomeTotal = ref(0)
const withdrawTotal = ref(0)

const {share} = useWxJs()


const jump_cash = () => {
  uni.navigateTo({
    url: '/pages/cash/cash'
  })
}

const init = async () => {
  commission.value = await getWithdrawMoneyAvailableData()
  incomeTotal.value = await getWithdrawIncomeTotalData()
  withdrawTotal.value = await getWithdrawTotalData()
  getPartnerInfo()
}

onShow(() => {
  init()
  share()
})
</script>


<style lang="less">
page {
  background-color: #F5F5F5;
}

/* 合伙人信息区域样式 */
.partner-header {
  margin-top: 20rpx;

  .grid {
    display: grid;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .gap-4 {
    gap: 1rem;
  }

  .bg-gray-50 {
    background-color: #f9fafb;
  }

  .p-3 {
    padding: 0.75rem;
  }

  .rounded-lg {
    border-radius: 0.5rem;
  }

  .text-gray-600 {
    color: #4b5563;
  }

  .text-gray-800 {
    color: #1f2937;
  }

  .font-medium {
    font-weight: 500;
  }

  .block {
    display: block;
  }

  .mb-1 {
    margin-bottom: 0.25rem;
  }

  .text-red-600 {
    color: #dc2626;
  }

  .text-yellow-600 {
    color: #d97706;
  }

  .text-green-600 {
    color: #16a34a;
  }

  .text-gray-600 {
    color: #4b5563;
  }
}

.reseller {
  background-color: #F5F5F5;
  min-height: 100vh;
  font-size: 14px;

  .top {
    color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    position: relative;
    //width: 100%;
    height: 250px;
    background-color: #864275;

    .title {
      background-color: #FFFFFF
    }

    .num {
      font-size: 15px;
      line-height: 25px;
      text-align: center;
      margin-top: 33%;
      width: 33%;

    }

    .num1 {
      margin-top: 10%;
      text-align: center;
      width: 33%;
    }

  }

  .jilu {
    font-size: 15px;
    position: absolute;
    left: 78%;
    top: 20%;
  }

  .tixian {
    display: flex;
    justify-content: center;
    background-color: #F5F5F5;
    width: 55%;
    height: 60px;
    border-radius: 60px;
    position: absolute;
    bottom: -30px;
    left: 23%;

    .btn {
      margin-top: 7%;
      text-align: center;
      width: 85%;
      height: 40px;
      border: none;
      border-radius: 50px;
      line-height: 40px;
      color: #FFFFFF;
    }


  }

  .icon {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    margin-left: 3%;
    margin-right: 3%;
    margin-top: 10%;

    .ico {
      border-radius: 10px;
      width: 49%;
      height: 100px;
      margin-top: 10px;
      display: flex;
      border: none;
      font-size: 15px;
      flex-direction: column;
      background-color: #FFFFFF;

      .img {
        width: 32px;
        height: 32px;
      }

      .tubiao {
        margin-top: 20px;
        text-align: center;
      }

      .text {
        color: #8F8F94;
        padding-top: 5px;
        text-align: center;

      }
    }
  }

  .kong {
    height: 100px;
  }
}
</style>
